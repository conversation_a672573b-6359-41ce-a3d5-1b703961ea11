{"name": "marten", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@emotion/react": "^11.14.0", "@mantine/core": "^8.3.3", "@mantine/hooks": "^8.3.3", "@tabler/icons-react": "^3.35.0", "@tanstack/react-router": "^1.132.37", "@tanstack/router-devtools": "^1.132.37", "@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@tauri-apps/cli": "^2", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "typescript": "~5.8.3", "vite": "^7.0.4"}}