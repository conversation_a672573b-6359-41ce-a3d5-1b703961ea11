import { AppShell, Text, NavLink, Group, Title } from '@mantine/core'
import { Outlet, Link, useLocation } from '@tanstack/react-router'
import { IconHome, IconCompass, IconSettings } from '@tabler/icons-react'

export function Layout() {
  const location = useLocation()

  const navItems = [
    { path: '/', label: 'Home', icon: IconHome },
    { path: '/explore', label: 'Explore', icon: IconCompass },
    { path: '/settings', label: 'Settings', icon: IconSettings },
  ]

  return (
    <AppShell
      navbar={{
        width: 250,
        breakpoint: 'sm',
      }}
      header={{ height: 60 }}
      padding="md"
    >
      <AppShell.Header>
        <Group h="100%" px="md">
          <Title order={3}>Marten</Title>
        </Group>
      </AppShell.Header>

      <AppShell.Navbar p="md">
        <Text size="sm" fw={500} mb="md">
          Navigation
        </Text>
        {navItems.map((item) => (
          <NavLink
            key={item.path}
            component={Link}
            to={item.path}
            label={item.label}
            leftSection={<item.icon size="1rem" />}
            active={location.pathname === item.path}
            mb="xs"
          />
        ))}
      </AppShell.Navbar>

      <AppShell.Main>
        <Outlet />
      </AppShell.Main>
    </AppShell>
  )
}
