import { createRouter, createRoute, createRootRoute } from '@tanstack/react-router'
import { Home } from './pages/Home'
import { Explore } from './pages/Explore'
import { Settings } from './pages/Settings'
import { Layout } from './components/Layout'

// Create the root route
const rootRoute = createRootRoute({
  component: Layout,
})

// Create the home route
const homeRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/',
  component: Home,
})

// Create the explore route
const exploreRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/explore',
  component: Explore,
})

// Create the settings route
const settingsRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/settings',
  component: Settings,
})

// Create the route tree
const routeTree = rootRoute.addChildren([homeRoute, exploreRoute, settingsRoute])

// Create the router
export const router = createRouter({ routeTree })

// Register the router instance for type safety
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router
  }
}
