import { useState } from "react";
import { Container, Title, Text, TextInput, Button, Group, Stack, Image, Anchor } from '@mantine/core';
import { invoke } from "@tauri-apps/api/core";
import reactLogo from "../assets/react.svg";

export function Home() {
  const [greetMsg, setGreetMsg] = useState("");
  const [name, setName] = useState("");

  async function greet() {
    // Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
    setGreetMsg(await invoke("greet", { name }));
  }

  return (
    <Container size="md">
      <Stack align="center" gap="xl">
        <Title order={1}>Welcome to Tauri + React</Title>

        <Group justify="center" gap="xl">
          <Anchor href="https://vite.dev" target="_blank">
            <Image src="/vite.svg" alt="Vite logo" w={100} h={100} />
          </Anchor>
          <Anchor href="https://tauri.app" target="_blank">
            <Image src="/tauri.svg" alt="Tauri logo" w={100} h={100} />
          </Anchor>
          <Anchor href="https://react.dev" target="_blank">
            <Image src={reactLogo} alt="React logo" w={100} h={100} />
          </Anchor>
        </Group>

        <Text ta="center">Click on the Tauri, Vite, and React logos to learn more.</Text>

        <form
          onSubmit={(e) => {
            e.preventDefault();
            greet();
          }}
        >
          <Group>
            <TextInput
              placeholder="Enter a name..."
              value={name}
              onChange={(e) => setName(e.currentTarget.value)}
              style={{ flex: 1 }}
            />
            <Button type="submit">Greet</Button>
          </Group>
        </form>

        {greetMsg && (
          <Text size="lg" fw={500}>
            {greetMsg}
          </Text>
        )}
      </Stack>
    </Container>
  );
}
