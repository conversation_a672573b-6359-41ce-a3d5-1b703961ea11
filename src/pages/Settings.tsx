import { Container, Title, Text, Card, Stack, Switch, Select, Button, Group, Divider } from '@mantine/core';
import { useState } from 'react';

export function Settings() {
  const [notifications, setNotifications] = useState(true);
  const [darkMode, setDarkMode] = useState(false);
  const [language, setLanguage] = useState('en');

  return (
    <Container size="md">
      <Title order={1} mb="xl">Settings</Title>
      <Text size="lg" mb="xl" c="dimmed">
        Customize your application preferences and configuration.
      </Text>

      <Stack gap="lg">
        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Title order={3} mb="md">General Settings</Title>
          
          <Stack gap="md">
            <Switch
              label="Enable notifications"
              description="Receive notifications about important updates"
              checked={notifications}
              onChange={(event) => setNotifications(event.currentTarget.checked)}
            />
            
            <Switch
              label="Dark mode"
              description="Use dark theme for the application"
              checked={darkMode}
              onChange={(event) => setDarkMode(event.currentTarget.checked)}
            />
            
            <Select
              label="Language"
              description="Choose your preferred language"
              value={language}
              onChange={(value) => setLanguage(value || 'en')}
              data={[
                { value: 'en', label: 'English' },
                { value: 'es', label: 'Spanish' },
                { value: 'fr', label: 'French' },
                { value: 'de', label: 'German' },
              ]}
            />
          </Stack>
        </Card>

        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Title order={3} mb="md">Account Settings</Title>
          
          <Stack gap="md">
            <Text size="sm" c="dimmed">
              Manage your account preferences and security settings.
            </Text>
            
            <Group>
              <Button variant="outline">Change Password</Button>
              <Button variant="outline">Export Data</Button>
            </Group>
          </Stack>
        </Card>

        <Divider />

        <Group justify="flex-end">
          <Button variant="outline">Reset to Defaults</Button>
          <Button>Save Changes</Button>
        </Group>
      </Stack>
    </Container>
  );
}
