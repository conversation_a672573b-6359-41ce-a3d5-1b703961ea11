import { Container, Title, Text, Card, SimpleGrid, Badge, Group } from '@mantine/core';

export function Explore() {
  const exploreItems = [
    {
      title: "Discover Features",
      description: "Explore the various features and capabilities of this application.",
      badge: "New"
    },
    {
      title: "Browse Content",
      description: "Find interesting content and resources curated for you.",
      badge: "Popular"
    },
    {
      title: "Community",
      description: "Connect with other users and share your experiences.",
      badge: "Active"
    },
    {
      title: "Tutorials",
      description: "Learn how to make the most of the application with step-by-step guides.",
      badge: "Learning"
    }
  ];

  return (
    <Container size="lg">
      <Title order={1} mb="xl">Explore</Title>
      <Text size="lg" mb="xl" c="dimmed">
        Discover new features, content, and opportunities to enhance your experience.
      </Text>

      <SimpleGrid cols={{ base: 1, sm: 2 }} spacing="lg">
        {exploreItems.map((item, index) => (
          <Card key={index} shadow="sm" padding="lg" radius="md" withBorder>
            <Group justify="space-between" mb="xs">
              <Text fw={500}>{item.title}</Text>
              <Badge color="blue" variant="light">
                {item.badge}
              </Badge>
            </Group>

            <Text size="sm" c="dimmed">
              {item.description}
            </Text>
          </Card>
        ))}
      </SimpleGrid>
    </Container>
  );
}
